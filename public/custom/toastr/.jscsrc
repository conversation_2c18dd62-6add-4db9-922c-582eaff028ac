{"excludeFiles": ["node_modules/**", "bower_components/**"], "requireCurlyBraces": null, "requireOperatorBeforeLineBreak": null, "requireCamelCaseOrUpperCaseIdentifiers": null, "maximumLineLength": null, "validateIndentation": 4, "validateQuoteMarks": null, "disallowMultipleLineStrings": null, "disallowMixedSpacesAndTabs": null, "disallowTrailingWhitespace": null, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowMultipleVarDecl": null, "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceBeforeBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "requireSpaceAfterBinaryOperators": true, "requireSpacesInConditionalExpression": true, "requireSpaceBeforeBlockStatements": true, "requireLineFeedAtFileEnd": null, "disallowSpacesInsideObjectBrackets": "all", "disallowSpacesInsideArrayBrackets": "all", "disallowSpacesInsideParentheses": true, "validateJSDoc": {"checkParamNames": true, "requireParamTypes": true}, "disallowMultipleLineBreaks": true, "disallowCommaBeforeLineBreak": null, "disallowDanglingUnderscores": null, "disallowEmptyBlocks": null, "disallowTrailingComma": null, "requireCapitalizedConstructors": null, "requireCommaBeforeLineBreak": null, "requireDotNotation": null, "requireMultipleVarDecl": null, "requireParenthesesAroundIIFE": true}