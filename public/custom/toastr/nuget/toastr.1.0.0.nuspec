<?xml version="1.0"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <version>1.0.0</version>
    <authors><PERSON>,<PERSON></authors>
    <owners><PERSON>,<PERSON></owners>
    <licenseUrl>http://www.opensource.org/licenses/mit-license.php</licenseUrl>
    <projectUrl>https://github.com/CodeSeven/toastr</projectUrl>
    <dependencies>
      <dependency id="jQuery" version="1.7.2" />
    </dependencies>
    <id>toastr</id>
    <title>toastr</title>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>3 Easy Steps:

1) Link to toastr.css  and toastr-responsive.css 
2) Link to toastr.js 
3) Use toastr to display a toast for info, success, warning or error

// Display an info toast with no title
toastr.info('Are you the 6 fingered man?')

*** For other API calls, see the demo</description>
    <summary>toastr is a Javascript library for Gnome / Growl type non-blocking notifications. jQuery is required. The goal is to create a simple core library that can be customized and extended.</summary>
    <copyright>Copyright © 2012 Hans Fjällemark &amp; <PERSON>.</copyright>
    <language>en-US</language>
    <tags>toastr, toast, notification, dialog, jquery</tags>
  </metadata>
  <files>
    <file src="content\content\toastr-responsive.css" target="content\content\toastr-responsive.css" />
    <file src="content\content\toastr.css" target="content\content\toastr.css" />
    <file src="content\scripts\toastr.js" target="content\scripts\toastr.js" />
  </files>
</package>